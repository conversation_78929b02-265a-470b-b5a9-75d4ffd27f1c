import {
  BedrockRuntimeClient,
  InvokeModelCommand,
} from "@aws-sdk/client-bedrock-runtime";
import logger from "../utils/logger";
import {
  AiChatRequest,
  AiChatResponse,
  CanvasStateForAI,
  AiErrorCode,
} from "../types/ai";
import { AiError } from "../middleware/aiErrorHandler";

export class BedrockService {
  private client: BedrockRuntimeClient;
  private modelId: string = "apac.anthropic.claude-3-7-sonnet-20250219-v1:0";

  constructor() {
    // 初始化Bedrock客户端
    this.client = new BedrockRuntimeClient({
      region: process.env.AWS_REGION || "us-west-2",
      // credentials: {
      //   accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
      //   secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
      // },
    });
  }

  /**
   * 调用Claude Sonnet进行AI对话
   * @param request 包含用户输入和画布状态的请求
   * @returns AI响应
   */
  async chat(request: AiChatRequest): Promise<AiChatResponse> {
    try {
      const { userInput, canvasState } = request;

      // 构建系统提示词
      const systemPrompt = this.buildSystemPrompt(canvasState);

      // 构建用户消息
      const userMessage = this.buildUserMessage(userInput, canvasState);

      // 准备Claude API请求
      const payload = {
        anthropic_version: "bedrock-2023-05-31",
        max_tokens: 8000,
        system: systemPrompt,
        messages: [
          {
            role: "user",
            content: userMessage,
          },
        ],
      };

      logger.info("调用Bedrock Claude Sonnet...");
      console.log(payload);
      const command = new InvokeModelCommand({
        modelId: this.modelId,
        body: JSON.stringify(payload),
        contentType: "application/json",
        accept: "application/json",
      });

      const response = await this.client.send(command);

      if (!response.body) {
        throw new Error("Bedrock响应为空");
      }

      // 解析响应
      const responseBody = JSON.parse(new TextDecoder().decode(response.body));
      const aiResponse = responseBody.content[0].text;

      logger.info("Bedrock调用成功");

      return {
        response: aiResponse,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error("Bedrock调用失败:", error);

      // 如果是AWS凭证问题，抛出特定错误
      if (error instanceof Error && error.message.includes("credentials")) {
        throw new AiError(
          "AWS凭证配置错误",
          AiErrorCode.AWS_CREDENTIALS_ERROR,
          500,
          "请检查AWS_ACCESS_KEY_ID、AWS_SECRET_ACCESS_KEY和AWS_REGION环境变量设置"
        );
      }

      // 如果是Bedrock API错误
      if (
        error instanceof Error &&
        (error.message.includes("Bedrock") || error.message.includes("bedrock"))
      ) {
        throw new AiError(
          "AI服务调用失败",
          AiErrorCode.BEDROCK_API_ERROR,
          500,
          error.message
        );
      }

      // 其他错误
      throw new AiError(
        "AI服务内部错误",
        AiErrorCode.INTERNAL_ERROR,
        500,
        error instanceof Error ? error.message : "未知错误"
      );
    }
  }

  /**
   * 构建系统提示词
   * @param canvasState 画布状态
   * @returns 系统提示词
   */
  private buildSystemPrompt(canvasState: CanvasStateForAI): string {
    return `
你是一位资深视频编辑技术专家，擅长分析和优化视频编辑项目的数据结构。用户会提供Canvas State对象，你需要进行专业分析并根据用户需求提供精确的建议。下面是Canvas State的结构：

### 1. Canvas State 对象
CanvasState {
  backgroundColor: string;           // 画布背景颜色 (如: "#111111")(必选）
  width: number;                    // 画布宽度 (必选）
  height: number;                   // 画布高度 (必选）
  globalCaptionStyle?: any;         // 全局字幕样式
  elements?: EditorElement[];        // 编辑器元素数组
  animations?: Animation[];          // 动画数组
  captions?: Caption[];             // 字幕数组
  tracks?: Track[];                 // 轨道数组
}

### 2. 编辑器元素 (EditorElement)
EditorElement {
  id: string;                      // 唯一标识符(必选）
  type: "video"| "image"| "audio"| "text" | "shape" | "gif"  // 元素类型(必选）
  name: string;                    // 元素名称
  placement?: Placement;            // 位置信息
  timeFrame?: TimeFrame;            // 时间帧信息
  properties?: ElementProperties;    // 元素属性
  trackId?: string;                // 所属轨道ID
  opacity?: number;                // 透明度 (默认: 1)
  action: "add" | "del" | "update"; // 操作类型(必选）
  align: "left" | "center" | "right" | "top" | "bottom |"hcenter"|"vcenter"; // 元素对齐方式(必选）
}

### 3. 位置信息 (Placement)
Placement = {
  x: number; // X坐标,坐标位置确保元素可见
  y: number; // Y坐标,坐标位置确保元素可见
  width: number; // 元素宽度，确保宽度不超过画布的宽度
  height: number; // 元素高度，确保高度不超过画布的高度

### 4. 时间帧信息 (TimeFrame)
TimeFrame {
  start: number;                   // 开始时间 (毫秒)
  end: number;                     // 结束时间 (毫秒)
}

### 5. 元素属性 (ElementProperties)
#### 视频元素属性
VideoProperties {
  src: string;                     // 视频源路径,引用网站的免费视频,例如:https://videos.pexels.com/video-files/2117231/2117231-hd_1920_1080_24fps.mp4
  volume?: number;                 // 音量 (0-1)
  playbackRate?: number;           // 播放速率
  muted?: boolean;                 // 是否静音
}
#### 图片元素属性
ImageProperties {
  src: string;                     // 图片源路径,引用Unsplash的免费照片
  filters?: any[];                 // 滤镜数组
}
#### 音频元素属性
AudioProperties {
  src: string;                     // 音频源路径，引用ncs.io的免费音乐
  volume?: number;                 // 音量 (0-1)
  loop?: boolean;                  // 是否循环
  fadeIn?: number;                 // 淡入时间
  fadeOut?: number;                // 淡出时间
}

#### 文本元素属性
TextProperties {
  text: string; // 文本内容
  fontSize: number; // 字体大小
  fontWeight: number; // 字体粗细
  fontFamily: string; // 字体族
  textAlign?: "left" | "center" | "right"; // 文本对齐
  fontColor?: string; // 十六进制字体颜色
  strokeWidth?: number; // 描边宽度
  strokeColor?: string; // 十六进制描边颜色
  shadowColor?: string; // 十六进制阴影颜色
  shadowOffsetX?: number; // 阴影X偏移
  shadowOffsetY?: number; // 阴影Y偏移
  backgroundColor?: string; // 背景颜色
}

#### 形状元素属性
ShapeProperties {
  shapeType:   "rect"| "circle"| "triangle"| "polygon"| "ellipse"| "line"| "pentagon"| "hexagon"| "octagon"| "roundedRect"| "arch"| "parallelogram"| "diamond"| "rightArrow"| "upArrow"| "cross"| "downArrow"| "wave"| "star"| "fourPointStar"| "sixPointStar"| "eightPointStar"| "sunBurst"| "semicircle"| "quarterCircle"| "ring"| "halfRing"| "plus";  // 形状类型
  fill?: string; // 填充颜色
  stroke?: string; // 边框颜色
  strokeWidth?: number;  // 边框宽度
  backgroundColor?: string; // 十六进制背景颜色 
}

### 6. 入场和出场动画对象 (Animation)
Animation {
  id: string;   // 动画ID(必选属性)
  targetId: string;  // 目标元素ID(必选属性)
  type: string; "fadeIn"|""fadeOut"|"slideIn"|"slideOut";  // 动画类型(必选属性)
  startTime: number;   // 开始时间(必选属性)
  duration: number;    // 持续时间(必选属性)
  group: string;  "in" | "out" //入场或出场动画(必选属性)
  properties: {
    direction: 'none'|'top' | 'right' | 'bottom' | 'left';//动画发生的方向(必选属性)
    useClipPath: boolean; // 是否使用裁剪路径(必选属性)
  };
  action: "add" | "del"; // 操作类型(必选属性)
}

### 7. 字幕对象 (Caption)
Caption {
  id: string;        // 字幕ID(必选属性)
  text: string;      // 字幕文本
  startTime: string; // 开始时间,时间格式为:"00:00:00",
  endTime: string;   // 结束时间,时间格式为:"00:00:00"
  action: "add" | "del" | "update"; // 操作类型(必选属性)
}
 
### 8. 轨道对象 (Track)
Track {
  id: string;  // 轨道ID(必选属性)
  name: string;   // 轨道名称(必选属性)
  type: TrackType;  // 轨道类型(必选属性)
  elementIds: string[];            // 包含的元素ID数组(必选属性)
  action: "add" | "del" ; // 操作类型(必选属性)
}
#### 轨道类型 (TrackType):
- "media" - 媒体轨道 (视频/图片/gif)
- "audio" - 音频轨道
- "text" - 文本轨道
- "caption" - 字幕轨道
 
9.### 全局字幕样式 (CaptionStyle)
CaptionStyle = {
  fontSize?: number; // 字体大小
  fontFamily?: string; // 字体
  fontColor?: string; // 十六进制字体颜色 
  fontWeight?: number; // 字体粗细
  textAlign?: "left" | "center" | "right"; // 文本对齐
  styles?: string[]; // 样式数组 (bold, italic, underline等)
  strokeWidth: number; // 描边宽度
  strokeColor?: string; // 十六进制描边颜色 
  shadowColor?: string; // 十六进制阴影颜色 
  shadowOffsetX?: number; // 阴影X偏移
  shadowOffsetY?: number; // 阴影Y偏移
  backgroundColor?: string; // 十六进制背景颜色
  width?: number; // 背景框宽度
  action: "update"; // 操作类型(必选属性)
};
 
### 注意:
1. **时间单位**: 所有时间相关的值都以毫秒为单位
2. **坐标系统**: 使用标准的2D坐标系统,坐标原点在左上角
3. **颜色格式**: 只支持十六进制颜色格式 (如: "#ffffff")
4. **元素层级**: 元素在数组中的顺序决定了渲染层级,避免需要显示的元素被最上层的元素遮挡
5. **轨道管理**: 每个元素都应该分配到相应的轨道中，如果元素在不同轨道需要同时显示，确保元素的坐标不会导致元素之间重叠
6. **动画同步**: 每个元素都应该分配到相应的动画，动画的时间应该与元素的时间帧保持同步

### Requirements:
1. 分析Canvas State对象并回答问题。
2. 提供编辑建议并生成修改后canvas state,并且只返回和旧canvas state对象不一样的对象和属性
3. 确保元素的布局美观，并且在同一轨道上所有元素的starttime和endtime不能重叠
4. 输出对象严格符合Canvas State 对象的定义，直接返回Canvas State json对象,不附加任何文字
`;
  }

  /**
   * 构建用户消息
   * @param userInput 用户输入
   * @param canvasState 画布状态
   * @returns 用户消息
   */
  private buildUserMessage(
    userInput: string,
    canvasState: CanvasStateForAI
  ): string {
    const canvasStateJson = JSON.stringify(canvasState, null, 2);

    return `用户问题：${userInput}

当前Canvas State详细信息：
\`\`\`json
${canvasStateJson}
\`\`\`

请基于提供的Canvas State信息，根据用户问题提供专业编辑修改：
1. 生成修改后的canvas state，仅返回变更的对象和属性以及CanvasState定义中必须的属性
2. 直接返回json格式，不包含任何额外文字说明或markdown标记
3. 确保返回的JSON格式能根据CanvasState定义有正确json对象嵌套关系
`;
  }

  /**
   * 分析画布元素
   * @param elements 元素数组
   * @returns 元素分析结果
   */
  private analyzeElements(elements: any[]): string {
    if (elements.length === 0) {
      return "项目中暂无任何元素。";
    }

    const elementTypes = elements.reduce((acc, element) => {
      acc[element.type] = (acc[element.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const typeAnalysis = Object.entries(elementTypes)
      .map(([type, count]) => {
        const typeName = this.getElementTypeName(type);
        return `- ${typeName}：${count}个`;
      })
      .join("\n");

    // 分析时间轴信息
    const timeAnalysis = this.analyzeTimeFrame(elements);

    // 分析轨道分布
    const trackAnalysis = this.analyzeTrackDistribution(elements);

    return `元素分布：\n${typeAnalysis}\n\n时间轴分析：\n${timeAnalysis}\n\n轨道分布：\n${trackAnalysis}`;
  }

  /**
   * 获取元素类型的中文名称
   * @param type 元素类型
   * @returns 中文名称
   */
  private getElementTypeName(type: string): string {
    const typeMap: Record<string, string> = {
      video: "视频",
      image: "图片",
      audio: "音频",
      text: "文本",
      shape: "形状",
    };
    return typeMap[type] || type;
  }

  /**
   * 分析时间轴信息
   * @param elements 元素数组
   * @returns 时间轴分析结果
   */
  private analyzeTimeFrame(elements: any[]): string {
    if (elements.length === 0) return "无时间轴数据";

    const timeFrames = elements
      .filter((el) => el.timeFrame)
      .map((el) => ({ start: el.timeFrame.start, end: el.timeFrame.end }));

    if (timeFrames.length === 0) return "无有效时间帧数据";

    const totalDuration = Math.max(...timeFrames.map((tf) => tf.end));
    const earliestStart = Math.min(...timeFrames.map((tf) => tf.start));

    return `- 项目总时长：${(totalDuration / 1000).toFixed(
      1
    )}秒\n- 最早开始时间：${(earliestStart / 1000).toFixed(1)}秒`;
  }

  /**
   * 分析轨道分布
   * @param elements 元素数组
   * @returns 轨道分布分析结果
   */
  private analyzeTrackDistribution(elements: any[]): string {
    const trackDistribution = elements.reduce((acc, element) => {
      const trackId = element.trackId || "未分配轨道";
      acc[trackId] = (acc[trackId] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const trackAnalysis = Object.entries(trackDistribution)
      .map(([trackId, count]) => `- ${trackId}：${count}个元素`)
      .join("\n");

    return trackAnalysis;
  }

  /**
   * 检查AWS凭证是否配置
   * @returns 是否配置了凭证
   */
  isConfigured(): boolean {
    return true;
  }
}
